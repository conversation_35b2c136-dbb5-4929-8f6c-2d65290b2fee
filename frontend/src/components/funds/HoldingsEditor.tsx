'use client';

import { useState, useEffect } from 'react';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import MonthSelector from '@/components/ui/MonthSelector';
import { Fund } from '@/types';
import { fundApi } from '@/lib/api';

interface HoldingItem {
  name: string;
  symbol: string;
  percentage: number;
  shares?: number;
  marketValue?: number;
  sector?: string;
}

interface HoldingsData {
  topHoldings: HoldingItem[];
  sectorAllocation: Record<string, number>;
  geographicAllocation: Record<string, number>;
  assetAllocation: Record<string, number>;
  marketCapAllocation: Record<string, number>;
  currencyAllocation: Record<string, number>;
  totalHoldingsCount?: number;
  holdingsConcentration?: number;
}

interface HoldingsEditorProps {
  fund: Fund;
  selectedMonth?: string; // YYYY-MM format
  availableMonths?: string[]; // Months that have snapshot data
  onSubmit: (month: string, data: HoldingsData) => Promise<void>;
  onCancel: () => void;
  disabled?: boolean;
}

const DEFAULT_SECTORS = [
  'Technology', 'Finance', 'Healthcare', 'Consumer', 'Industrial', 
  'Energy', 'Materials', 'Utilities', 'Real Estate', 'Communication'
];

const DEFAULT_REGIONS = [
  'North America', 'Europe', 'Asia Pacific', 'India', 'China', 
  'Japan', 'Latin America', 'Middle East', 'Africa', 'Others'
];

const DEFAULT_ASSETS = [
  'Equity', 'Debt', 'Cash', 'Commodities', 'Real Estate', 'Others'
];

const DEFAULT_MARKET_CAPS = [
  'Large Cap', 'Mid Cap', 'Small Cap', 'Micro Cap'
];

const DEFAULT_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'INR', 'CNY', 'AUD', 'CAD', 'Others'
];

export default function HoldingsEditor({
  fund,
  selectedMonth,
  availableMonths = [],
  onSubmit,
  onCancel,
  disabled = false
}: HoldingsEditorProps) {
  const [currentMonth, setCurrentMonth] = useState<string>(
    selectedMonth || new Date().toISOString().slice(0, 7)
  );
  const [topHoldings, setTopHoldings] = useState<HoldingItem[]>([]);
  const [sectorAllocation, setSectorAllocation] = useState<Record<string, number>>({});
  const [geographicAllocation, setGeographicAllocation] = useState<Record<string, number>>({});
  const [assetAllocation, setAssetAllocation] = useState<Record<string, number>>({});
  const [marketCapAllocation, setMarketCapAllocation] = useState<Record<string, number>>({});
  const [currencyAllocation, setCurrencyAllocation] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);
  const [loadingSnapshot, setLoadingSnapshot] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [snapshotStatus, setSnapshotStatus] = useState<'loading' | 'found' | 'not_found' | 'error' | null>(null);

  // Initialize with existing data
  useEffect(() => {
    if (fund.holdings) {
      // Top Holdings
      if (fund.holdings.topHoldings) {
        setTopHoldings(fund.holdings.topHoldings.map(h => ({
          name: h.name || '',
          symbol: h.symbol || '',
          percentage: typeof h.percentage === 'string' ? parseFloat(h.percentage) : h.percentage || 0,
          shares: h.shares,
          marketValue: typeof h.marketValue === 'string' ? parseFloat(h.marketValue) : h.marketValue || 0,
          sector: h.sector
        })));
      }

      // Allocations
      if (fund.holdings.sectorAllocation) {
        const allocation: Record<string, number> = {};
        Object.entries(fund.holdings.sectorAllocation).forEach(([key, value]) => {
          allocation[key] = typeof value === 'string' ? parseFloat(value) : (value as number);
        });
        setSectorAllocation(allocation);
      }

      if (fund.holdings.geographicAllocation) {
        const allocation: Record<string, number> = {};
        Object.entries(fund.holdings.geographicAllocation).forEach(([key, value]) => {
          allocation[key] = typeof value === 'string' ? parseFloat(value) : (value as number);
        });
        setGeographicAllocation(allocation);
      }

      if (fund.holdings.assetAllocation) {
        const allocation: Record<string, number> = {};
        Object.entries(fund.holdings.assetAllocation).forEach(([key, value]) => {
          allocation[key] = typeof value === 'string' ? parseFloat(value) : (value as number);
        });
        setAssetAllocation(allocation);
      }

      if (fund.holdings.marketCapAllocation) {
        const allocation: Record<string, number> = {};
        Object.entries(fund.holdings.marketCapAllocation).forEach(([key, value]) => {
          allocation[key] = typeof value === 'string' ? parseFloat(value) : (value as number);
        });
        setMarketCapAllocation(allocation);
      }

      if (fund.holdings.currencyAllocation) {
        const allocation: Record<string, number> = {};
        Object.entries(fund.holdings.currencyAllocation).forEach(([key, value]) => {
          allocation[key] = typeof value === 'string' ? parseFloat(value) : (value as number);
        });
        setCurrencyAllocation(allocation);
      }
    }
  }, [fund]);

  // Load snapshot data when month changes
  useEffect(() => {
    const loadSnapshotData = async () => {
      if (!currentMonth) {
        setSnapshotStatus(null);
        return;
      }

      setLoadingSnapshot(true);
      setSnapshotStatus('loading');
      try {
        const response = await fundApi.getFundSnapshot(fund.id, currentMonth);
        if (response.success && response.data && response.data.holdings) {
          const holdings = response.data.holdings;

          // Load top holdings
          if (holdings.topHoldings) {
            setTopHoldings(holdings.topHoldings.map((h: any) => ({
              name: h.name || '',
              symbol: h.symbol || '',
              percentage: typeof h.percentage === 'string' ? parseFloat(h.percentage) : h.percentage || 0,
              shares: h.shares,
              marketValue: typeof h.marketValue === 'string' ? parseFloat(h.marketValue) : h.marketValue || 0,
              sector: h.sector
            })));
          }

          // Load allocations
          if (holdings.sectorAllocation) {
            const allocation: Record<string, number> = {};
            Object.entries(holdings.sectorAllocation).forEach(([key, value]) => {
              allocation[key] = typeof value === 'string' ? parseFloat(value as string) : (value as number);
            });
            setSectorAllocation(allocation);
          }

          if (holdings.geographicAllocation) {
            const allocation: Record<string, number> = {};
            Object.entries(holdings.geographicAllocation).forEach(([key, value]) => {
              allocation[key] = typeof value === 'string' ? parseFloat(value as string) : (value as number);
            });
            setGeographicAllocation(allocation);
          }

          if (holdings.assetAllocation) {
            const allocation: Record<string, number> = {};
            Object.entries(holdings.assetAllocation).forEach(([key, value]) => {
              allocation[key] = typeof value === 'string' ? parseFloat(value as string) : (value as number);
            });
            setAssetAllocation(allocation);
          }

          if (holdings.marketCapAllocation) {
            const allocation: Record<string, number> = {};
            Object.entries(holdings.marketCapAllocation).forEach(([key, value]) => {
              allocation[key] = typeof value === 'string' ? parseFloat(value as string) : (value as number);
            });
            setMarketCapAllocation(allocation);
          }

          if (holdings.currencyAllocation) {
            const allocation: Record<string, number> = {};
            Object.entries(holdings.currencyAllocation).forEach(([key, value]) => {
              allocation[key] = typeof value === 'string' ? parseFloat(value as string) : (value as number);
            });
            setCurrencyAllocation(allocation);
          }
          setSnapshotStatus('found');
        } else {
          setSnapshotStatus('not_found');
        }
      } catch (error) {
        console.log('No existing holdings snapshot for month:', currentMonth);
        // Check if it's a 404 error (snapshot not found) vs other errors
        if (error instanceof Error && error.message.includes('404')) {
          setSnapshotStatus('not_found');
        } else {
          setSnapshotStatus('error');
          console.error('Error loading holdings snapshot:', error);
        }
      } finally {
        setLoadingSnapshot(false);
      }
    };

    loadSnapshotData();
  }, [currentMonth, fund.id]);

  const addHolding = () => {
    setTopHoldings([...topHoldings, {
      name: '',
      symbol: '',
      percentage: 0,
      shares: 0,
      marketValue: 0,
      sector: DEFAULT_SECTORS[0]
    }]);
  };

  const removeHolding = (index: number) => {
    setTopHoldings(topHoldings.filter((_, i) => i !== index));
  };

  const updateHolding = (index: number, field: keyof HoldingItem, value: any) => {
    const newHoldings = [...topHoldings];
    newHoldings[index] = {
      ...newHoldings[index],
      [field]: value
    };
    setTopHoldings(newHoldings);
  };

  const updateAllocation = (
    type: 'sector' | 'geographic' | 'asset' | 'marketCap' | 'currency',
    key: string,
    value: number
  ) => {
    switch (type) {
      case 'sector':
        setSectorAllocation({ ...sectorAllocation, [key]: value });
        break;
      case 'geographic':
        setGeographicAllocation({ ...geographicAllocation, [key]: value });
        break;
      case 'asset':
        setAssetAllocation({ ...assetAllocation, [key]: value });
        break;
      case 'marketCap':
        setMarketCapAllocation({ ...marketCapAllocation, [key]: value });
        break;
      case 'currency':
        setCurrencyAllocation({ ...currencyAllocation, [key]: value });
        break;
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate top holdings
    const totalHoldingsPercentage = topHoldings.reduce((sum, h) => sum + h.percentage, 0);
    if (totalHoldingsPercentage > 100) {
      newErrors.topHoldings = 'Total holdings percentage cannot exceed 100%';
    }

    // Validate allocations sum to ~100%
    const validateAllocation = (allocation: Record<string, number>, name: string) => {
      const total = Object.values(allocation).reduce((sum, val) => sum + val, 0);
      if (total > 0 && (total < 95 || total > 105)) {
        newErrors[name] = `${name} allocation should sum to approximately 100% (currently ${total.toFixed(1)}%)`;
      }
    };

    validateAllocation(sectorAllocation, 'Sector');
    validateAllocation(geographicAllocation, 'Geographic');
    validateAllocation(assetAllocation, 'Asset');
    validateAllocation(marketCapAllocation, 'Market Cap');
    validateAllocation(currencyAllocation, 'Currency');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!currentMonth) {
      setErrors({ submit: 'Please select a month' });
      return;
    }

    // Calculate holdings concentration (top 10)
    const top10Holdings = topHoldings.slice(0, 10);
    const holdingsConcentration = top10Holdings.reduce((sum, h) => sum + h.percentage, 0);

    const holdingsData: HoldingsData = {
      topHoldings: topHoldings.filter(h => h.name && h.percentage > 0),
      sectorAllocation,
      geographicAllocation,
      assetAllocation,
      marketCapAllocation,
      currencyAllocation,
      totalHoldingsCount: topHoldings.filter(h => h.name).length,
      holdingsConcentration
    };

    await onSubmit(currentMonth, holdingsData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Month Selection */}
      <Card>
        <Card.Header>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Holdings Data Period</h2>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Select Month *
              </label>
              <MonthSelector
                value={currentMonth}
                onChange={setCurrentMonth}
                availableMonths={availableMonths}
                disabled={loadingSnapshot || disabled}
                placeholder="Select month for holdings data"
              />
              {loadingSnapshot && (
                <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                  Loading existing holdings data for {currentMonth}...
                </p>
              )}
              {!loadingSnapshot && availableMonths.includes(currentMonth) && (
                <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                  ✓ Holdings data exists for {currentMonth} - editing existing snapshot
                </p>
              )}
              {!loadingSnapshot && !availableMonths.includes(currentMonth) && (
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Creating new holdings snapshot for {currentMonth}
                </p>
              )}
              {errors.submit && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
              )}
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Snapshot Status Notification */}
      {snapshotStatus && snapshotStatus !== 'loading' && (
        <div className="px-6 py-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          {snapshotStatus === 'found' && (
            <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  Existing holdings snapshot found for {currentMonth}
                </p>
                <p className="text-xs text-green-600 dark:text-green-400">
                  Form has been pre-filled with existing holdings data. You can modify and update the snapshot.
                </p>
              </div>
            </div>
          )}

          {snapshotStatus === 'not_found' && (
            <div className="flex items-center p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                  No holdings snapshot found for {currentMonth}
                </p>
                <p className="text-xs text-amber-600 dark:text-amber-400">
                  You are creating a new holdings snapshot. Enter the holdings data below and submit to save.
                </p>
              </div>
            </div>
          )}

          {snapshotStatus === 'error' && (
            <div className="flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error loading holdings snapshot data for {currentMonth}
                </p>
                <p className="text-xs text-red-600 dark:text-red-400">
                  There was an error loading the snapshot data. Please try again or contact support if the issue persists.
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Top Holdings */}
      <Card>
        <Card.Header>
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Top Holdings</h2>
            <Button type="button" size="sm" onClick={addHolding}>
              Add Holding
            </Button>
          </div>
        </Card.Header>
        <Card.Content>
          {topHoldings.length === 0 ? (
            <p className="text-gray-600 dark:text-gray-400 text-center py-4">
              No holdings added yet. Click "Add Holding" to start.
            </p>
          ) : (
            <div className="space-y-4">
              {topHoldings.map((holding, index) => (
                <div key={index} className="grid grid-cols-12 gap-4 items-end">
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Name
                    </label>
                    <input
                      type="text"
                      value={holding.name}
                      onChange={(e) => updateHolding(index, 'name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="Company name"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Symbol
                    </label>
                    <input
                      type="text"
                      value={holding.symbol}
                      onChange={(e) => updateHolding(index, 'symbol', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="TICKER"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Weight (%)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={holding.percentage}
                      onChange={(e) => updateHolding(index, 'percentage', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="0.00"
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Sector
                    </label>
                    <select
                      value={holding.sector || DEFAULT_SECTORS[0]}
                      onChange={(e) => updateHolding(index, 'sector', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      {DEFAULT_SECTORS.map(sector => (
                        <option key={sector} value={sector}>{sector}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Market Value
                    </label>
                    <input
                      type="number"
                      value={holding.marketValue}
                      onChange={(e) => updateHolding(index, 'marketValue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      placeholder="0"
                    />
                  </div>
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeHolding(index)}
                      className="w-full"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
          {errors.topHoldings && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.topHoldings}</p>
          )}
        </Card.Content>
      </Card>

      {/* Allocations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sector Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Sector Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_SECTORS.map(sector => (
                <div key={sector} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{sector}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={sectorAllocation[sector] || 0}
                      onChange={(e) => updateAllocation('sector', sector, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors.Sector && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.Sector}</p>
            )}
          </Card.Content>
        </Card>

        {/* Geographic Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Geographic Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_REGIONS.map(region => (
                <div key={region} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{region}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={geographicAllocation[region] || 0}
                      onChange={(e) => updateAllocation('geographic', region, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors.Geographic && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.Geographic}</p>
            )}
          </Card.Content>
        </Card>

        {/* Asset Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Asset Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_ASSETS.map(asset => (
                <div key={asset} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{asset}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={assetAllocation[asset] || 0}
                      onChange={(e) => updateAllocation('asset', asset, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors.Asset && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.Asset}</p>
            )}
          </Card.Content>
        </Card>

        {/* Market Cap Allocation */}
        <Card>
          <Card.Header>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Market Cap Allocation</h3>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {DEFAULT_MARKET_CAPS.map(cap => (
                <div key={cap} className="flex items-center justify-between">
                  <label className="text-sm text-gray-700 dark:text-gray-300">{cap}</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      step="0.1"
                      value={marketCapAllocation[cap] || 0}
                      onChange={(e) => updateAllocation('marketCap', cap, parseFloat(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <span className="text-sm text-gray-600 dark:text-gray-400">%</span>
                  </div>
                </div>
              ))}
            </div>
            {errors['Market Cap'] && (
              <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors['Market Cap']}</p>
            )}
          </Card.Content>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={disabled}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={disabled}
        >
          Save Holdings
        </Button>
      </div>
    </form>
  );
}