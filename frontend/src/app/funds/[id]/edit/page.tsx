'use client';

import { useEffect, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import { Edit, TrendingUp, BusinessCenter, Analytics } from '@mui/icons-material';

import FundEditForm from '@/components/funds/FundEditForm';
import HoldingsEditor from '@/components/funds/HoldingsEditor';
import KPIRiskMetricsForm from '@/components/funds/KPIRiskMetricsForm';
import MarketDataInputForm from '@/components/funds/MarketDataInput';
import { Card, Button, Tabs } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';
import { FundDetails, Fund, MarketDataInput } from '@/types';
import { safeINR, safeUSD, safeToFixed } from '@/utils';


export default function FundEditPage() {
  const params = useParams();
  const router = useRouter();
  const fundId = params.id as string;
  const { t } = useTranslation();
  
  const [fundDetails, setFundDetails] = useState<FundDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saveLoading, setSaveLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [availableMonths, setAvailableMonths] = useState<string[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<string>(
    new Date().toISOString().slice(0, 7)
  );

  useEffect(() => {
    fetchFundDetails();
    fetchAvailableMonths();
  }, [fundId]);

  const fetchFundDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fundApi.getFundDetails(fundId);
      setFundDetails(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch fund details');
      console.error('Failed to fetch fund details:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableMonths = async () => {
    try {
      const response = await fundApi.listFundSnapshots(fundId);
      if (response.success && response.data) {
        const months = response.data.map((snapshot: any) => snapshot.snapshot_month);
        setAvailableMonths(months);
      }
    } catch (err) {
      console.error('Failed to fetch available months:', err);
      // Don't set error state for this, as it's not critical
    }
  };

  const handleSaveFund = async (fundData: Partial<Fund>) => {
    try {
      setSaveLoading(true);
      setError(null);
      
      console.log('Saving fund data:', fundData);
      
      // Call API to update fund
      const response = await fundApi.updateFund(fundId, fundData);
      
      console.log('Fund updated successfully:', response.data);
      
      // Show success message and redirect
      alert(t('funds.fundUpdatedSuccessfully'));
      router.push(`/funds/${fundId}`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update fund';
      setError(errorMessage);
      console.error('Failed to update fund:', err);
      alert(t('funds.errorUpdatingFund', { error: errorMessage }));
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/funds/${fundId}`);
  };

  const handleMarketDataSubmit = async (month: string, marketData: Partial<MarketDataInput>) => {
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      console.log('Submitting market data snapshot for month:', month, marketData);

      // Create snapshot data with market data
      const snapshotData = {
        market_data: marketData,
        nav: marketData.nav,
        total_assets: marketData.totalAssets,
      };

      const response = await fundApi.createFundSnapshot(fundId, month, snapshotData);

      if (response.success) {
        setSuccess(`Market data snapshot created successfully for ${month}!`);

        // Refresh fund details and available months
        await fetchFundDetails();
        await fetchAvailableMonths();

        // Show success message
        setTimeout(() => {
          setSuccess(null);
        }, 5000);
      } else {
        setError(response.message || 'Failed to create market data snapshot');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create market data snapshot';
      setError(errorMessage);
      console.error('Error creating market data snapshot:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleHoldingsSubmit = async (month: string, holdingsData: any) => {
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      console.log('Submitting holdings snapshot for month:', month, holdingsData);

      // Create snapshot data with holdings
      const snapshotData = {
        holdings: holdingsData,
      };

      const response = await fundApi.createFundSnapshot(fundId, month, snapshotData);

      if (response.success) {
        setSuccess(`Holdings snapshot created successfully for ${month}!`);

        // Refresh fund details and available months
        await fetchFundDetails();
        await fetchAvailableMonths();

        // Show success message
        setTimeout(() => {
          setSuccess(null);
        }, 5000);
      } else {
        setError(response.message || 'Failed to create holdings snapshot');
      }
    } catch (err) {
      setError('Failed to create holdings snapshot');
      console.error('Error creating holdings snapshot:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleKPIRiskMetricsSubmit = async (month: string, analyticsData: any) => {
    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      console.log('Submitting KPI and Risk Metrics snapshot for month:', month, analyticsData);

      // Create snapshot data with performance metrics and risk analytics
      const snapshotData = {
        performance_metrics: analyticsData.analytics?.kpis,
        risk_analytics: analyticsData.analytics?.riskMetrics,
      };

      const response = await fundApi.createFundSnapshot(fundId, month, snapshotData);

      if (response.success) {
        setSuccess(`KPI and Risk Metrics snapshot created successfully for ${month}!`);

        // Refresh fund details and available months
        await fetchFundDetails();
        await fetchAvailableMonths();

        // Show success message
        setTimeout(() => {
          setSuccess(null);
        }, 5000);
      } else {
        setError(response.message || 'Failed to create KPI and Risk Metrics snapshot');
      }
    } catch (err) {
      setError('Failed to create KPI and Risk Metrics snapshot');
      console.error('Error creating KPI and Risk Metrics snapshot:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('funds.loadingFunds')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <Card.Content>
            <div className="text-center">
              <h3 className="text-lg font-medium text-red-600 mb-2">{t('common.error')}</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <div className="flex gap-2 justify-center">
                <Button onClick={fetchFundDetails} variant="outline">
                  {t('funds.tryAgain')}
                </Button>
                <Button onClick={() => router.push('/funds')}>
                  {t('funds.backToFunds')}
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (!fundDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <Card.Content>
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-800 mb-2">{t('funds.fundNotFound')}</h3>
              <p className="text-gray-600 mb-4">{t('funds.fundNotFoundMessage')}</p>
              <Button onClick={() => router.push('/funds')}>
                {t('funds.backToFunds')}
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  // Define tabs configuration
  const tabsConfig = [
    {
      id: 'details',
      label: t('funds.editDetails'),
      icon: <Edit className="w-4 h-4" />,
      children: (
        <div>
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
              <p className="text-red-600 dark:text-red-300 text-sm">{error}</p>
            </div>
          )}
          
          <FundEditForm
            fund={fundDetails}
            isEditing={true}
            onSubmit={handleSaveFund}
            onCancel={handleCancel}
            loading={saveLoading}
          />
        </div>
      )
    },
    {
      id: 'market-data',
      label: t('funds.inputMonthlyMarketData'),
      icon: <TrendingUp className="w-4 h-4" />,
      children: (
        <div>
          {/* Success Message */}
          {success && (
            <div className="mb-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      {success}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Error Message */}
          {error && (
            <div className="mb-6">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      {error}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <MarketDataInputForm
            fund={fundDetails as Fund}
            selectedMonth={selectedMonth}
            availableMonths={availableMonths}
            onSubmit={handleMarketDataSubmit}
            onCancel={() => {}}
            className={submitting ? 'opacity-50 pointer-events-none' : ''}
          />
        </div>
      )
    },
    {
      id: 'holdings',
      label: t('funds.manageHoldings'),
      icon: <BusinessCenter className="w-4 h-4" />,
      children: (
        <div>
          {/* Success Message */}
          {success && (
            <div className="mb-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      {success}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Error Message */}
          {error && (
            <div className="mb-6">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      {error}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <HoldingsEditor
            fund={fundDetails as Fund}
            selectedMonth={selectedMonth}
            availableMonths={availableMonths}
            onSubmit={handleHoldingsSubmit}
            onCancel={() => {}}
            disabled={submitting}
          />
        </div>
      )
    },
    {
      id: 'kpi-risk-metrics',
      label: 'KPI & Risk Metrics',
      icon: <Analytics className="w-4 h-4" />,
      children: (
        <div>
          {/* Success Message */}
          {success && (
            <div className="mb-6">
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      {success}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Error Message */}
          {error && (
            <div className="mb-6">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      {error}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <KPIRiskMetricsForm
            fund={fundDetails}
            selectedMonth={selectedMonth}
            availableMonths={availableMonths}
            onSubmit={handleKPIRiskMetricsSubmit}
            onCancel={() => {}}
            disabled={submitting}
          />
        </div>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors" style={{ fontFamily: 'Lato, sans-serif' }}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button 
              variant="outline" 
              onClick={handleCancel}
              className="flex items-center gap-2"
            >
              ← {t('common.back')}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {t('funds.editFundTitle', { name: fundDetails.name })}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {fundDetails.symbol} • {fundDetails.type.replace('_', ' ').toUpperCase()}
              </p>
            </div>
          </div>
        </div>

        {/* Tabbed Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-colors">
          <Tabs tabs={tabsConfig} defaultActiveTab="details" />
        </div>

        {/* Current Fund Info for Reference */}
        <Card className="mt-6">
          <Card.Header>
            <Card.Title>{t('funds.currentFundInformation')}</Card.Title>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {t('funds.referenceFundInfo')}
            </p>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.currentNAV')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{safeUSD(fundDetails.nav)}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.aum')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">${safeToFixed(fundDetails.aum ? fundDetails.aum / 1000000 : null, 1)}M</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.expenseRatio')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fundDetails.expenseRatio}%</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.riskLevel')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100 capitalize">{fundDetails.riskLevel}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.fundManager')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fundDetails.fundManager}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.rating')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fundDetails.rating}/5 ⭐</div>
              </div>
            </div>
          </Card.Content>
        </Card>
        
        {/* Loading Overlay */}
        {submitting && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-4">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{t('common.saving')}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('common.pleaseWait')}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 