'use client';

import { useState, useEffect } from 'react';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import MonthSelector from '@/components/ui/MonthSelector';
import { MarketDataInput, Fund, FundSnapshotCreate } from '@/types';
import { fundApi } from '@/lib/api';

interface MarketDataInputProps {
  fund: Fund;
  selectedMonth?: string; // YYYY-MM format
  availableMonths?: string[]; // Months that have snapshot data
  onSubmit: (month: string, data: Partial<MarketDataInput>) => Promise<void>;
  onCancel: () => void;
  className?: string;
}

export default function MarketDataInputForm({
  fund,
  selectedMonth,
  availableMonths = [],
  onSubmit,
  onCancel,
  className = ''
}: MarketDataInputProps) {
  const [currentMonth, setCurrentMonth] = useState<string>(
    selectedMonth || new Date().toISOString().slice(0, 7)
  );
  const [formData, setFormData] = useState<Partial<MarketDataInput>>({
    fundId: fund.id,
    dataTimestamp: new Date(),
    inputBy: '', // This should come from user context
    validated: false,
  });

  const [loading, setLoading] = useState(false);
  const [loadingSnapshot, setLoadingSnapshot] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [snapshotStatus, setSnapshotStatus] = useState<'loading' | 'found' | 'not_found' | 'error' | null>(null);

  // Load snapshot data when month changes
  useEffect(() => {
    const loadSnapshotData = async () => {
      if (!currentMonth) {
        setSnapshotStatus(null);
        return;
      }

      setLoadingSnapshot(true);
      setSnapshotStatus('loading');

      // Clear any previous form data when changing months
      setFormData(prev => ({
        fundId: fund.id,
        dataTimestamp: new Date(),
        inputBy: prev.inputBy || '', // Keep inputBy across month changes
        validated: false,
      }));

      try {
        const response = await fundApi.getFundSnapshot(fund.id, currentMonth);
        if (response.success && response.data) {
          // Populate form with snapshot data
          const snapshot = response.data;
          setFormData(prev => ({
            ...prev,
            nav: snapshot.nav ? parseFloat(snapshot.nav) : undefined,
            // Add other market data fields from snapshot
            ...(snapshot.market_data || {}),
          }));
          setSnapshotStatus('found');
        } else {
          setSnapshotStatus('not_found');
        }
      } catch (error) {
        console.log('No existing snapshot for month:', currentMonth);
        // Check if it's a 404 error (snapshot not found) vs other errors
        if (error instanceof Error && error.message.includes('404')) {
          setSnapshotStatus('not_found');
        } else {
          setSnapshotStatus('error');
          console.error('Error loading snapshot:', error);
        }
      } finally {
        setLoadingSnapshot(false);
      }
    };

    loadSnapshotData();
  }, [currentMonth, fund.id]);

  const handleInputChange = (field: keyof MarketDataInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.inputBy?.trim()) {
      newErrors.inputBy = 'Input by field is required';
    }
    
    if (!formData.dataTimestamp) {
      newErrors.dataTimestamp = 'Data timestamp is required';
    }
    
    // Validate numeric fields
    if (formData.nav !== undefined && formData.nav <= 0) {
      newErrors.nav = 'NAV must be greater than 0';
    }
    
    if (formData.marketPrice !== undefined && formData.marketPrice <= 0) {
      newErrors.marketPrice = 'Market price must be greater than 0';
    }
    
    if (formData.volume !== undefined && formData.volume < 0) {
      newErrors.volume = 'Volume cannot be negative';
    }
    
    if (formData.priceToBook !== undefined && formData.priceToBook < 0) {
      newErrors.priceToBook = 'P/B ratio cannot be negative';
    }
    
    if (formData.priceToEarnings !== undefined && formData.priceToEarnings < 0) {
      newErrors.priceToEarnings = 'P/E ratio cannot be negative';
    }
    
    if (formData.dividendYield !== undefined && formData.dividendYield < 0) {
      newErrors.dividendYield = 'Dividend yield cannot be negative';
    }
    
    if (formData.volatility !== undefined && formData.volatility < 0) {
      newErrors.volatility = 'Volatility cannot be negative';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!currentMonth) {
      setErrors({ submit: 'Please select a month' });
      return;
    }

    setLoading(true);
    try {
      await onSubmit(currentMonth, formData);
    } catch (error) {
      console.error('Error submitting market data:', error);
      setErrors({ submit: 'Failed to submit market data. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card>
        <Card.Header>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Monthly Market Data Input - {fund.name}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Input monthly market data for {fund.symbol}. Market data is updated on a monthly basis.
          </p>
        </Card.Header>

        {/* Snapshot Status Notification */}
        {snapshotStatus && snapshotStatus !== 'loading' && (
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            {snapshotStatus === 'found' && (
              <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    Existing snapshot found for {currentMonth}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    Form has been pre-filled with existing data. You can modify and update the snapshot.
                  </p>
                </div>
              </div>
            )}

            {snapshotStatus === 'not_found' && (
              <div className="flex items-center p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
                    No snapshot found for {currentMonth}
                  </p>
                  <p className="text-xs text-amber-600 dark:text-amber-400">
                    This will create a new monthly snapshot. Fill in the market data below.
                  </p>
                </div>
              </div>
            )}

            {snapshotStatus === 'error' && (
              <div className="flex items-center p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800 dark:text-red-200">
                    Error loading snapshot data for {currentMonth}
                  </p>
                  <p className="text-xs text-red-600 dark:text-red-400">
                    There was an error checking for existing data. You can still proceed to create a new snapshot.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        <Card.Content>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Input By *
                </label>
                <input
                  type="text"
                  value={formData.inputBy || ''}
                  onChange={(e) => handleInputChange('inputBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                  placeholder="Your name or ID"
                />
                {errors.inputBy && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.inputBy}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Monthly Data Period *
                </label>
                <MonthSelector
                  value={currentMonth}
                  onChange={(month) => {
                    setCurrentMonth(month);
                    // Update the dataTimestamp in formData
                    const [year, monthNum] = month.split('-');
                    const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
                    handleInputChange('dataTimestamp', date);
                  }}
                  availableMonths={availableMonths}
                  disabled={loadingSnapshot}
                  placeholder="Select month for data input"
                />
                {snapshotStatus === 'loading' && (
                  <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                    Loading existing data for {currentMonth}...
                  </p>
                )}
                {snapshotStatus === 'found' && (
                  <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                    ✓ Data exists for {currentMonth} - editing existing snapshot
                  </p>
                )}
                {snapshotStatus === 'not_found' && (
                  <p className="mt-1 text-xs text-amber-600 dark:text-amber-400">
                    ⚠️ No snapshot found for {currentMonth} - creating new snapshot
                  </p>
                )}
                {snapshotStatus === 'error' && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                    ❌ Error loading snapshot data for {currentMonth}
                  </p>
                )}
                {errors.dataTimestamp && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dataTimestamp}</p>
                )}
              </div>
            </div>

            {/* Price Data Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Monthly Price Data</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    NAV
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.nav || ''}
                    onChange={(e) => handleInputChange('nav', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Monthly Net Asset Value"
                  />
                  {errors.nav && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.nav}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Market Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.marketPrice || ''}
                    onChange={(e) => handleInputChange('marketPrice', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Month-end market price"
                  />
                  {errors.marketPrice && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.marketPrice}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Volume
                  </label>
                  <input
                    type="number"
                    value={formData.volume || ''}
                    onChange={(e) => handleInputChange('volume', parseInt(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Monthly trading volume"
                  />
                  {errors.volume && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.volume}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Valuation Metrics Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Monthly Valuation Metrics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    P/B Ratio
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.priceToBook || ''}
                    onChange={(e) => handleInputChange('priceToBook', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Price-to-Book ratio"
                  />
                  {errors.priceToBook && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.priceToBook}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    P/E Ratio
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.priceToEarnings || ''}
                    onChange={(e) => handleInputChange('priceToEarnings', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Price-to-Earnings ratio"
                  />
                  {errors.priceToEarnings && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.priceToEarnings}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Dividend Yield (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.dividendYield || ''}
                    onChange={(e) => handleInputChange('dividendYield', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Dividend yield percentage"
                  />
                  {errors.dividendYield && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dividendYield}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Risk Metrics Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Monthly Risk Metrics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Volatility (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.volatility || ''}
                    onChange={(e) => handleInputChange('volatility', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Volatility percentage"
                  />
                  {errors.volatility && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.volatility}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Beta
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.beta || ''}
                    onChange={(e) => handleInputChange('beta', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Beta coefficient"
                  />
                  {errors.beta && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.beta}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Notes Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes || ''}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                placeholder="Additional notes about this monthly data input..."
              />
            </div>

            {/* Validation */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="validated"
                checked={formData.validated || false}
                onChange={(e) => handleInputChange('validated', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="validated" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                Mark this data as validated
              </label>
            </div>

            {formData.validated && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Validation Notes
                </label>
                <textarea
                  value={formData.validationNotes || ''}
                  onChange={(e) => handleInputChange('validationNotes', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                  placeholder="Validation notes..."
                />
              </div>
            )}

            {/* Error Display */}
            {errors.submit && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <p className="text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
              >
                {loading ? 'Submitting...' : 'Submit Market Data'}
              </Button>
            </div>
          </form>
        </Card.Content>
      </Card>
    </div>
  );
}
